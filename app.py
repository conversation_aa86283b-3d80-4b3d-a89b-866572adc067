from flask import Flask, render_template, request, jsonify
import requests

app = Flask(__name__)

@app.route("/networks", methods=['GET'])
def get_networks():

    # Get query parameters
    city = request.args.get('city')
    name = request.args.get('name')
    
    # Base URL for CityBikes API
    base_url = "http://api.citybik.es/v2/networks"

    
    # Get networks data from CityBikes API
    response = requests.get(base_url)
    networks_data = response.json()['networks']
    
    return jsonify(networks_data)


@app.route("/networks", methods=['POST'])
def post_networks():
    # Get JSON data from request
    network_data = request.get_json()
    
    # Validate required fields
    if not network_data or not all(key in network_data for key in ['name', 'location']):
        return jsonify({'error': 'Missing required fields'}), 400
        
    # Add network to local database
    # Note: This is a placeholder - you'll need to implement actual database storage
    try:
        # Here you would typically:
        # 1. Create a database model instance
        # 2. Add it to the session
        # 3. Commit the transaction
        
        return jsonify({'message': 'Network added successfully', 'data': network_data}), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    pass

if __name__ == "__main__":
    app.run(debug=True)
